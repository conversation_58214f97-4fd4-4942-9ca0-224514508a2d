name: Test, Build and Deploy

on:
  push:
    branches: [master, develop, dekker-dev1, dekker-dev3]
  pull_request:

jobs:
  ### BUILD ###
  build:
    name: Build ${{ matrix.name }} production dist
    env:
      BUILD_ENV: ${{ matrix.buildPath }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        include:
          - name: ET
            buildPath: production-et
            isEnabled: ${{ github.ref == 'refs/heads/master'}}
          - name: LV
            buildPath: production-lv
            isEnabled: ${{ github.ref == 'refs/heads/master'}}
          - name: LT
            buildPath: production-lt
            isEnabled: ${{ github.ref == 'refs/heads/master'}}
          - name: LT-NEW
            buildPath: production-lt-new
            isEnabled: ${{ github.ref == 'refs/heads/master'}}
          - name: staging-lt
            buildPath: staging-lt
            isEnabled: ${{ github.ref == 'refs/heads/master'}}
          - name: dekker-dev1
            buildPath: dekker-dev1
            isEnabled: ${{ github.ref == 'refs/heads/dekker-dev1'}}
          - name: dekker-dev3
            buildPath: dekker-dev3
            isEnabled: ${{ github.ref == 'refs/heads/dekker-dev3'}}

    steps:
      - uses: actions/checkout@v4

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - uses: pnpm/action-setup@v3
        name: Install pnpm
        with:
          version: 9
          run_install: false

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - uses: actions/cache@v4
        name: Setup pnpm cache
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        if: ${{ matrix.isEnabled }}
        run: pnpm install

      - name: 'Build'
        if: ${{ matrix.isEnabled }}
        run: pnpm run build:${{ matrix.buildPath }}

      - name: 'Lint and format'
        if: ${{ matrix.isEnabled }}
        run: |
          pnpm run lint
          pnpm run format

      - name: Upload artifact for deployment job
        if: ${{ matrix.isEnabled }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.name }}
          path: build

  ### DEPLOY ###
  deploy:
    name: Deploy ${{ matrix.name }} files to S3
    needs: build
    runs-on: ubuntu-latest
    if: github.event_name == 'push'
    strategy:
      matrix:
        include:
          - name: ET
            distPath: ET
            folder: esto-purchase-flow-front-ee
            distribution: E3JH3SCDQKKULY
            isEnabled: ${{ github.ref == 'refs/heads/master'}}
          - name: LV
            distPath: LV
            folder: esto-purchase-flow-front-lv
            distribution: EW2PUVD2YN4F9
            isEnabled: ${{ github.ref == 'refs/heads/master'}}
          - name: LT
            distPath: LT
            folder: esto-purchase-flow-front-lt
            distribution: EX73N2VKNSVIU
            isEnabled: ${{ github.ref == 'refs/heads/master'}}
          - name: LT-NEW
            distPath: LT-NEW
            folder: esto-purchase-flow-front-lt-new
            distribution: E30DIYYK311LQP
            isEnabled: ${{ github.ref == 'refs/heads/master'}}
          - name: staging-lt
            distPath: staging-lt
            folder: esto-purchase-flow-front-staging-lt
            distribution: EVPC3VOWNYG4O
            isEnabled: ${{ github.ref == 'refs/heads/master'}}
          - name: dekker-dev1
            distPath: dekker-dev1
            folder: esto-purchase-flow-front-dev-1
            distribution: E1ZZX0FSQ75ROA
            isEnabled: ${{ github.ref == 'refs/heads/dekker-dev1'}}
          - name: dekker-dev3
            distPath: dekker-dev3
            folder: esto-purchase-flow-front-dev-3
            distribution: E3V5BG8OID3UUP
            isEnabled: ${{ github.ref == 'refs/heads/dekker-dev3'}}

    steps:
      - name: 'Download dist'
        if: ${{ matrix.isEnabled }}
        uses: actions/download-artifact@v4
        with:
          name: ${{ matrix.distPath }}
          path: build

      - name: 'Configure AWS credentials'
        if: ${{ matrix.isEnabled }}
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-central-1

      - name: 'Deploy to S3'
        if: ${{ matrix.isEnabled }}
        run: |
          aws s3 sync ./build s3://${{ matrix.folder }}/public --delete
          aws cloudfront create-invalidation --distribution-id ${{ matrix.distribution }} --paths /index.html /assets/{i18n,images,vector-images}/*
