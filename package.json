{"name": "purchase-flow-front", "version": "0.1.0", "private": true, "dependencies": {"@apollo/client": "3.9.9", "@graphql-codegen/near-operation-file-preset": "3.0.0", "@hookform/resolvers": "3.10.0", "@radix-ui/react-accordion": "1.2.3", "@radix-ui/react-checkbox": "1.1.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.6", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-radio-group": "1.2.3", "@radix-ui/react-select": "2.1.6", "@radix-ui/react-separator": "1.1.2", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tooltip": "1.1.6", "@react-spring/types": "9.7.3", "@rudderstack/analytics-js": "3.7.11", "@sentry/cli": "2.30.2", "@sentry/react": "7.108.0", "@sentry/vite-plugin": "2.16.1", "@vitejs/plugin-react-swc": "3.7.2", "apollo-upload-client": "18.0.1", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "date-fns": "3.6.0", "embla-carousel-autoplay": "8.5.2", "embla-carousel-fade": "8.5.2", "embla-carousel-react": "8.5.2", "env-cmd": "10.1.0", "graphql": "16.8.1", "i18next": "23.10.1", "i18next-browser-languagedetector": "7.2.0", "i18next-locize-backend": "6.4.1", "lodash": "4.17.21", "lucide-react": "0.474.0", "posthog-js": "1.249.3", "react": "18.2.0", "react-dom": "18.2.0", "react-helmet-async": "2.0.5", "react-hook-form": "7.51.1", "react-i18next": "14.1.0", "react-loading-skeleton": "3.4.0", "react-number-format": "5.4.3", "react-popper-tooltip": "4.4.2", "react-router-dom": "6.22.3", "react-toastify": "10.0.5", "react-use": "17.5.1", "sass": "1.72.0", "sonner": "2.0.1", "tailwind-merge": "2.6.0", "tailwindcss-animate": "1.0.7", "uuid": "9.0.1", "vite": "6.0.11", "vite-tsconfig-paths": "4.3.2", "zod": "3.24.1"}, "scripts": {"dev": "env-cmd -e local vite", "build": "tsc && env-cmd -e local vite build", "build:dekker-dev1": "env-cmd -e dekker-dev1 vite build", "build:dekker-dev3": "env-cmd -e dekker-dev3 vite build", "build:staging-lt": "env-cmd -e staging-lt vite build", "build:production-et": "env-cmd -e production-et vite build", "build:production-lt": "env-cmd -e production-lt vite build", "build:production-lt-new": "env-cmd -e production-lt-new vite build", "build:production-lv": "env-cmd -e production-lv vite build", "codegen:purchase-flow": "graphql-codegen -c codegen-purchase-flow.ts", "codegen:core": "graphql-codegen -c codegen-core.ts", "print-version": "node src/scripts/print-version.js", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "lint-and-format": "pnpm run lint:fix && pnpm run format", "ts-check": "tsc --noEmit", "git-hooks-install": "lefthook install", "tailwind": "npx tailwindcss -i ./src/styles/tailwind.css -o ./src/styles/index.css --watch"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.16.0", "@tanstack/eslint-plugin-query": "^5.72.2", "@typescript-eslint/eslint-plugin": "^8.17.0", "@typescript-eslint/parser": "^8.17.0", "eslint": "9.16.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-tailwindcss": "^3.18.0", "globals": "^15.13.0", "prettier": "^3.4.2", "@graphql-codegen/cli": "5.0.2", "@graphql-codegen/typescript-operations": "4.2.0", "@graphql-codegen/typescript-react-apollo": "4.3.0", "@savvywombat/tailwindcss-grid-areas": "4.0.0", "@types/apollo-upload-client": "18.0.0", "@types/jest": "29.5.12", "@types/lodash": "4.17.15", "@types/node": "20.11.30", "@types/react": "18.2.70", "@types/react-dom": "18.2.22", "@types/uuid": "9.0.8", "@vitejs/plugin-react-swc": "3.7.2", "autoprefixer": "10.4.20", "i": "0.3.7", "postcss": "8.5.0", "tailwindcss": "3.4.17", "types": "link:react-number-format/types/types", "typescript": "5.4.5", "vite-plugin-svgr": "4.2.0", "vite-plugin-webfont-dl": "3.10.4"}}