import { ContractType } from 'api/core/generated';
import {
  ABBREVIATIONS_BY_LANGUAGES_MAP,
  AppRegions,
  AppSearchParams,
  AppSigningMethods,
  BANKLINK_PAYMENT_POLL_STATUSES,
  FormFieldNames,
  LocalStorageKeys,
  LOCIZE_ERRORS_TRANSLATION_KEYS,
  LOCIZE_LOGIN_TRANSLATION_KEYS,
  LocizeNamespaces,
  PAYSERA_PAYMENT_STATUSES,
  SpouseConsentPageViewTypes,
} from 'app-constants';
import { region } from 'environment';
import { useGetBanklinkPaymentStatus } from 'hooks/use-banklink-payment-status-poll';
import { useBanklinkSigningPoll } from 'hooks/use-banklink-signing-poll';
import { useGetBanks } from 'hooks/use-get-banklinks';
import { useGetCreditLineWithdrawalByHash } from 'hooks/use-get-credit-account-withdrawal-by-hash';
import { useSignContractWithBanklink } from 'hooks/use-sign-contract-with-banklink';
import { useSignContractWithMobileId } from 'hooks/use-sign-contract-with-mobile-id';
import { useSignContractWithSmartId } from 'hooks/use-sign-contract-with-smart-id';
import { useUpdateSpouseUserInfo } from 'hooks/use-update-spouse-user-info';
import { useEffectOnce } from 'hooks/utils';
import type { Option, SpouseConsentPageLogic } from 'models';
import { useEffect, useMemo, useState } from 'react';
import type { FieldValues, UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  extractValidationErrors,
  formatCreditLineWithdrawalToSpouseConsentPageDataFormat,
  getEmploymentDateOptions,
  removeFromStorage,
  roundNumberUpByTwoDecimals,
  setToStorage,
} from 'services';

export const useSpouseConsentPageLogic = (): SpouseConsentPageLogic => {
  const [searchParams] = useSearchParams();
  const [processingSpouseSigning, setProcessingSpouseSigning] = useState(false);
  const payseraSigningPaymentStatus = searchParams.get(
    AppSearchParams.payseraSigningPaymentStatus,
  );

  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const { t: tl, i18n } = useTranslation(LocizeNamespaces.login);
  const { t: te } = useTranslation(LocizeNamespaces.errors);

  const {
    getCreditLineWithdrawal,
    creditLineWithdrawal,
    creditLineWithdrawalLoading,
  } = useGetCreditLineWithdrawalByHash();

  const {
    spouseEmploymentDate,
    spouseMonthlyLivingExpenses,
    spouseExpenditureMonthly,
    spouseNetIncomeMonthly,
    productId,
    productIdVariables,
    applicationUserInfoId,
    spouseName,
    spouseOverdueDebt,
  } =
    formatCreditLineWithdrawalToSpouseConsentPageDataFormat(
      creditLineWithdrawal,
    );

  const [
    selectedSpouseConsentSigningMethod,
    setSelectedSpouseConsentSigningMethod,
  ] = useState<Option | null>(null);

  const formConfig = {
    defaultValues: {
      [FormFieldNames.spouseNetIncomeMonthly]: spouseNetIncomeMonthly,
      [FormFieldNames.spouseExpenditureMonthly]: spouseExpenditureMonthly ?? 0,
      [FormFieldNames.spouseMonthlyLivingExpenses]: spouseMonthlyLivingExpenses,
      [FormFieldNames.spouseTotalExpenses]: roundNumberUpByTwoDecimals(
        Number(spouseMonthlyLivingExpenses ?? 0) +
          Number(spouseExpenditureMonthly ?? 0),
      ),
      [FormFieldNames.spouseEmploymentDate]: spouseEmploymentDate,
      [FormFieldNames.spouseOverdueDebt]: spouseOverdueDebt ?? 0,
      [FormFieldNames.spouseSigningMethod]:
        selectedSpouseConsentSigningMethod?.value,
    },
  };

  const isBanklinkSigningAllowed = region !== AppRegions.et;

  const { getBanklinks, banklinks } = useGetBanks();
  const { updateSpouseInfo, spouseInfoUpdateError } = useUpdateSpouseUserInfo();
  const {
    signContractWithSmartId,
    prepareSmartIdContractSignature,
    smartIdContractSignaturePreparationChallenge,
    smartIdContractSignaturePreparationLoading,
    smartIdContractSignaturePreparationError,
  } = useSignContractWithSmartId();
  const {
    signContractWithMobileId,
    prepareMobileIdContractSignature,
    mobileIdContractSignaturePreparationChallenge,
    mobileIdContractSignaturePreparationError,
    mobileIdContractSignaturePreparationLoading,
  } = useSignContractWithMobileId();
  const {
    prepareBanklinkContractSignature,
    banklinkSigningError,
    banklinkSigningAcceptUrl,
    banklinkSigningCancelUrl,
  } = useSignContractWithBanklink();
  const {
    banklinkPaymentStatus,
    startBanklinkPaymentStatusPolling,
    stopBanklinkPaymentStatusPolling,
  } = useGetBanklinkPaymentStatus();
  const {
    isBanklinkSigningPollSuccess,
    startBanklinkSigningStatusPolling,
    stopBanklinkSigningStatusPolling,
  } = useBanklinkSigningPoll();

  const [spouseConsentPageViewType, setSpouseConsentPageViewType] = useState(
    SpouseConsentPageViewTypes.signing,
  );

  const spouseConsentContractType = ContractType.SPOUSE_CONSENT;

  const spouseConsentFormValidationErrors = extractValidationErrors(
    spouseInfoUpdateError ||
      mobileIdContractSignaturePreparationError ||
      smartIdContractSignaturePreparationError ||
      banklinkSigningError,
  );

  const isMobileIdSigningMethod =
    selectedSpouseConsentSigningMethod?.value === AppSigningMethods.mobileId;
  const isBanklinkSigningMethod =
    selectedSpouseConsentSigningMethod?.value === AppSigningMethods.banklink;

  const spouseConsentEmploymentDateOptions = getEmploymentDateOptions(t);
  const spouseConsentSigningMethodSelectOptions = [
    {
      value: AppSigningMethods.mobileId,
      label: tl(LOCIZE_LOGIN_TRANSLATION_KEYS.mobileIdMethodButtonLabel),
    },
    {
      value: AppSigningMethods.smartId,
      label: tl(LOCIZE_LOGIN_TRANSLATION_KEYS.smartIdMethodButtonLabel),
    },
    {
      value: AppSigningMethods.banklink,
      label: tl(LOCIZE_LOGIN_TRANSLATION_KEYS.banklinkMethodButtonLabel),
    },
  ];

  const disablingEmptyFieldNames =
    {
      [AppSigningMethods.mobileId]: [FormFieldNames.phone],
      [AppSigningMethods.banklink]: [FormFieldNames.paymentMethodKey],
      [AppSigningMethods.smartId]: [],
    }[selectedSpouseConsentSigningMethod?.value ?? ''] ?? [];

  const onChangeTotalSpouseExpenses = (
    formValues: FieldValues,

    formMethods: UseFormReturn<FieldValues, any>,
  ) => {
    const { setValue } = formMethods;
    const { spouse_expenditure_monthly, spouse_monthly_living_expenses } =
      formValues;

    setValue(
      FormFieldNames.spouseTotalExpenses,
      Number(spouse_expenditure_monthly) +
        Number(spouse_monthly_living_expenses) || 0,
    );
  };

  const spouseConsentPageLoaded = Boolean(
    !creditLineWithdrawalLoading && productId,
  );

  const signAppWithMobileId = () => {
    return signContractWithMobileId()
      .then(({ data }) => {
        if (data?.success) {
          executeSuccessfulSigningCallbacks();
        } else {
          toast.error(
            te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError) as string,
          );
        }
      })
      .catch(() => {
        setSpouseConsentPageViewType(SpouseConsentPageViewTypes.signing);
      });
  };

  const signAppByMobileIdOrSmartId = () => {
    const signInMethod = selectedSpouseConsentSigningMethod?.value;

    if (signInMethod === AppSigningMethods.mobileId) {
      return signAppWithMobileId();
    }

    return signAppWithSmartId();
  };

  const signAppWithSmartId = () => {
    return signContractWithSmartId()
      .then(({ data }) => {
        if (data?.success) {
          executeSuccessfulSigningCallbacks();
        } else {
          toast.error(
            te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError) as string,
          );
        }
      })
      .catch(() => {
        setSpouseConsentPageViewType(SpouseConsentPageViewTypes.signing);
      });
  };

  const onPinConfirmationCancel = () => {
    setSpouseConsentPageViewType(SpouseConsentPageViewTypes.signing);
  };

  const executeSuccessfulSigningCallbacks = () => {
    setSpouseConsentPageViewType(SpouseConsentPageViewTypes.success);
  };

  const prepareSigningAppWithSmartId = () => {
    prepareSmartIdContractSignature({
      contract_type: spouseConsentContractType,
      ...productIdVariables,
    }).then(() => {
      setSpouseConsentPageViewType(SpouseConsentPageViewTypes.pinConfirmation);
    });
  };

  const prepareSigningAppWithMobileId = ({ phone }: FieldValues) => {
    prepareMobileIdContractSignature({
      contract_type: spouseConsentContractType,
      phone,
      ...productIdVariables,
    }).then(() => {
      setSpouseConsentPageViewType(SpouseConsentPageViewTypes.pinConfirmation);
    });
  };

  const signAppWithBanklink = ({ payment_method_key }: FieldValues) => {
    return prepareBanklinkContractSignature({
      contract_type: spouseConsentContractType,
      payment_method_key,
      accept_url: banklinkSigningAcceptUrl,
      cancel_url: banklinkSigningCancelUrl,
      ...productIdVariables,
    }).then(({ data }) => {
      const redirectUrl = data?.challenge?.redirect_url;
      const sessionId = data?.challenge?.session_id;

      if (sessionId) {
        setToStorage(LocalStorageKeys.sessionId, sessionId);
      }

      if (redirectUrl) {
        window.location.href = redirectUrl;
      }
    });
  };

  const onSigningSpouseConsentForm = (formFieldValues: FieldValues) => {
    switch (selectedSpouseConsentSigningMethod?.value) {
      case AppSigningMethods.banklink:
        return signAppWithBanklink(formFieldValues);
      case AppSigningMethods.mobileId:
        return prepareSigningAppWithMobileId(formFieldValues);
      default:
        return prepareSigningAppWithSmartId();
    }
  };

  const onSpouseConsentFormSubmit = async (formFieldValues: FieldValues) => {
    const {
      spouse_expenditure_monthly,
      spouse_monthly_living_expenses,
      spouse_net_income_monthly,
      spouse_employment_date,
      spouse_overdue_debt,
    } = formFieldValues;

    setProcessingSpouseSigning(true);

    try {
      await updateSpouseInfo({
        application_user_info_id: applicationUserInfoId ?? 0,
        spouse_net_income_monthly: +spouse_net_income_monthly || null,
        spouse_expenditure_monthly:
          spouse_expenditure_monthly !== ''
            ? +spouse_expenditure_monthly
            : null,
        spouse_monthly_living_expenses:
          spouse_monthly_living_expenses !== ''
            ? +spouse_monthly_living_expenses
            : null,
        spouse_employment_date: spouse_employment_date || null,
        spouse_overdue_debt: +spouse_overdue_debt || 0,
        reject_when_necessary: false,
      });

      await onSigningSpouseConsentForm(formFieldValues);
    } finally {
      setProcessingSpouseSigning(false);
    }
  };

  useEffectOnce(() => {
    getCreditLineWithdrawal();
  });

  useEffect(() => {
    if (isBanklinkSigningPollSuccess) {
      stopBanklinkSigningStatusPolling();
      executeSuccessfulSigningCallbacks();
    }
  }, [isBanklinkSigningPollSuccess]);

  useEffect(() => {
    if (
      isBanklinkSigningAllowed &&
      isBanklinkSigningMethod &&
      !banklinks.length
    ) {
      getBanklinks(ABBREVIATIONS_BY_LANGUAGES_MAP[i18n.language]);
    }
  }, [isBanklinkSigningMethod]);

  useEffect(() => {
    if (payseraSigningPaymentStatus === PAYSERA_PAYMENT_STATUSES.successful) {
      setSpouseConsentPageViewType(SpouseConsentPageViewTypes.pending);
      startBanklinkPaymentStatusPolling().catch(() => {
        setSpouseConsentPageViewType(SpouseConsentPageViewTypes.signing);
      });
    } else if (
      payseraSigningPaymentStatus === PAYSERA_PAYMENT_STATUSES.failed
    ) {
      toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError) as string);
    }
  }, []);

  useEffect(() => {
    switch (banklinkPaymentStatus) {
      case BANKLINK_PAYMENT_POLL_STATUSES.success:
        stopBanklinkPaymentStatusPolling();
        startBanklinkSigningStatusPolling()
          .then(({ data }) => {
            if (data?.success) {
              stopBanklinkSigningStatusPolling();
              removeFromStorage(LocalStorageKeys.sessionId);
              executeSuccessfulSigningCallbacks();
            }
          })
          .catch((error) => {
            // Stop polling only if the code is not 3803
            // 3803 means that the Payment was not confirmed by Paysera
            if (error.code !== 3803) {
              stopBanklinkPaymentStatusPolling();
            }
          });
        break;
      case BANKLINK_PAYMENT_POLL_STATUSES.failed:
        stopBanklinkPaymentStatusPolling();
        removeFromStorage(LocalStorageKeys.sessionId);
        break;
      default:
        break;
    }
  }, [banklinkPaymentStatus]);

  return useMemo(
    () => ({
      onPinConfirmationCancel,
      disablingEmptyFieldNames,
      processingSpouseSigning:
        processingSpouseSigning ||
        smartIdContractSignaturePreparationLoading ||
        mobileIdContractSignaturePreparationLoading,
      onSpouseConsentFormSubmit,
      spouseConsentPageLoaded,
      spouseConsentPageFormConfig: formConfig,
      processingSpouseConsentPage: creditLineWithdrawalLoading,
      spouseConsentFormValidationErrors,
      onChangeTotalSpouseExpenses,
      spouseName,
      spouseConsentEmploymentDateOptions,
      spouseConsentSigningMethodSelectOptions,
      selectedSpouseConsentSigningMethod,
      setSelectedSpouseConsentSigningMethod,
      banklinkOptions: banklinks,
      isBanklinkSigningAllowed,
      spouseConsentPageViewType,
      smartIdSpouseSignaturePollChallengeId:
        smartIdContractSignaturePreparationChallenge?.challenge_id || '',
      mobileIdSpouseSignaturePollChallengeId:
        mobileIdContractSignaturePreparationChallenge?.challenge_id || '',
      isMobileIdSigningMethod,
      isBanklinkSigningMethod,
      signAppByMobileIdOrSmartId,
    }),
    [
      onPinConfirmationCancel,
      disablingEmptyFieldNames,
      processingSpouseSigning,
      onSpouseConsentFormSubmit,
      spouseConsentPageLoaded,
      formConfig,
      creditLineWithdrawalLoading,
      spouseConsentFormValidationErrors,
      onChangeTotalSpouseExpenses,
      spouseName,
      spouseConsentEmploymentDateOptions,
      spouseConsentSigningMethodSelectOptions,
      selectedSpouseConsentSigningMethod,
      banklinks,
      isBanklinkSigningAllowed,
      spouseConsentPageViewType,
      smartIdContractSignaturePreparationChallenge,
      mobileIdContractSignaturePreparationChallenge,
      smartIdContractSignaturePreparationLoading,
      mobileIdContractSignaturePreparationLoading,
      isMobileIdSigningMethod,
      isBanklinkSigningMethod,
      signAppByMobileIdOrSmartId,
    ],
  );
};
