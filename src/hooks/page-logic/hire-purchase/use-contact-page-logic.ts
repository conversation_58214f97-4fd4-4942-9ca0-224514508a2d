import { PoliticalExposure } from 'api/core/generated';
import {
  FormFieldNames,
  GoogleAnalyticsEvents,
  LOCIZE_USER_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
  PURCHASE_FLOW_LOG_ACTIONS,
} from 'app-constants';
import { useRootContext } from 'context/root';
import { regionPhonePrefix } from 'environment';
import { useGetCurrentApplication } from 'hooks/use-get-current-application';
import { useGetPageAttributes } from 'hooks/use-get-page-attributes';
import { useGetPoliticalExposures } from 'hooks/use-get-political-exposures';
import { useLogApplicationAction } from 'hooks/use-log-application-action';
import { useUpdateUserInfo } from 'hooks/use-update-user-info';
import { useUpdateUserTerms } from 'hooks/use-update-user-terms';
import { useEffectOnce } from 'hooks/utils';
import type { Option } from 'models';
import { useEffect, useMemo, useState } from 'react';
import type { FieldValues, UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  convertPageAttributeNamesToObject,
  extractValidationErrors,
  filterObjectByExistingKeysInObject,
  formatApplicationToContactPageDataFormat,
} from 'services';

export const useContactPageLogic = () => {
  const { t } = useTranslation(LocizeNamespaces.user);

  const { getApplication, application, applicationLoading } =
    useGetCurrentApplication();

  const [processingContactPage, setProcessingContactPage] = useState(false);

  const { logAction } = useLogApplicationAction();
  const {
    getPageUrlAndNavigate,
    pageUrlAndNavigationProcessing,
    trackGoogleAnalyticsEvent,
  } = useRootContext();
  const { pageAttributes, pageAttributesLoading, getPageAttributes } =
    useGetPageAttributes();
  const {
    getPoliticalExposures,
    politicalExposures,
    politicalExposuresLoading,
  } = useGetPoliticalExposures();
  const { updateUserInfo, userInfoError } = useUpdateUserInfo();
  const { updateUserTerms } = useUpdateUserTerms();

  const userInfoValidationErrors = extractValidationErrors(userInfoError);

  const visiblePageAttributes = useMemo(
    () => convertPageAttributeNamesToObject(pageAttributes),
    [pageAttributes],
  );

  const shouldShowPoliticalExposureSelect =
    visiblePageAttributes[PageAttributeNames.politicalExposureSelect];

  const {
    fullName,
    email,
    phone,
    address,
    city,
    postCode,
    iban,
    politicalExposure,
    applicationUserInfoId,
    userId,
    productId,
    newsletterAgreement,
    conditionsAgreement,
    phoneAreaCode,
    allowPensionQuery,
  } = useMemo(
    () => formatApplicationToContactPageDataFormat(application),
    [application],
  );

  const contactPageFormConfig = useMemo(
    () => ({
      defaultValues: {
        [FormFieldNames.name]: fullName ?? '',
        [FormFieldNames.email]: email ?? undefined,
        [FormFieldNames.phone]: phone ?? undefined,
        [FormFieldNames.address]: address ?? undefined,
        [FormFieldNames.city]: city ?? undefined,
        [FormFieldNames.postCode]: postCode ?? undefined,
        [FormFieldNames.iban]: iban ?? '',
        [FormFieldNames.politicalExposure]: t(
          `${LOCIZE_USER_TRANSLATION_KEYS.politicalExposurePrefix}${
            politicalExposure ?? PoliticalExposure.NONE
          }`,
        ),
        [FormFieldNames.conditionsAgreement]: conditionsAgreement ?? undefined,
        [FormFieldNames.conditionsAndPensionAgreement]:
          (conditionsAgreement && allowPensionQuery) ?? undefined,
        [FormFieldNames.newsletterAgreement]: newsletterAgreement ?? undefined,
      },
    }),
    [
      t,
      fullName,
      email,
      phone,
      address,
      city,
      postCode,
      iban,
      politicalExposure,
      conditionsAgreement,
      newsletterAgreement,
      allowPensionQuery,
    ],
  );

  const getPoliticalExposureOptions = (): Array<Option> =>
    politicalExposures?.map((politicalExposure) => ({
      label: t(
        `${LOCIZE_USER_TRANSLATION_KEYS.politicalExposurePrefix}${politicalExposure}`,
      ),
      value: politicalExposure,
    })) ?? [];

  const onContactFormSubmit = async (
    {
      phone,
      address,
      post_code,
      city,
      email,
      political_exposure,
      conditions_agreement,
      conditions_and_pension_agreement,
      newsletter_agreement,
      iban,
    }: FieldValues,
    formMethods: UseFormReturn<FieldValues>,
  ) => {
    setProcessingContactPage(true);

    if (!userId) {
      throw new Error('User ID is missing');
    }

    const variablesFilteredByVisiblePageAttributes =
      filterObjectByExistingKeysInObject(
        {
          email,
          phone,
          address,
          post_code,
          city,
          iban,
        },
        formMethods.control._fields,
      );

    let conditionsAgreementValue = conditions_agreement ?? conditionsAgreement;
    let allowPensionQueryValue = allowPensionQuery;

    if (
      visiblePageAttributes[
        PageAttributeNames.termsAndConditionsAndPensionCheckbox
      ]
    ) {
      conditionsAgreementValue = conditions_and_pension_agreement;
      allowPensionQueryValue = conditions_and_pension_agreement;
    }

    try {
      // Order of operations is important here
      await updateUserTerms({
        user_id: userId,
        newsletter_agreement: newsletter_agreement ?? newsletterAgreement,
        conditions_agreement: conditionsAgreementValue,
        allow_pension_query: allowPensionQueryValue ?? false,
        political_exposure,
      });
      await updateUserInfo({
        application_user_info_id: applicationUserInfoId ?? 0,
        ...variablesFilteredByVisiblePageAttributes,
      });

      await getPageUrlAndNavigate(true);

      trackGoogleAnalyticsEvent(GoogleAnalyticsEvents.contactCompleted);
    } catch (error) {
      if ((error as Error)?.message === 'Unauthorized') {
        await getPageUrlAndNavigate(true);
      }
    } finally {
      setProcessingContactPage(false);
    }
  };

  useEffectOnce(() => {
    getPageAttributes();
  });

  useEffectOnce(() => {
    const handleApplication = async () => {
      const { data } = await getApplication();
      if (data?.application?.user_info) {
        const {
          first_name,
          last_name,
          phone,
          address,
          email,
          city,
          post_code,
        } = data.application.user_info;

        // we had a situation where the application didn't have the user info
        if (
          !first_name &&
          !last_name &&
          !phone &&
          !address &&
          !email &&
          !city &&
          !post_code
        ) {
          await getApplication();
        }
      }
    };

    handleApplication();
  });

  useEffect(() => {
    if (shouldShowPoliticalExposureSelect && !politicalExposures?.length) {
      getPoliticalExposures();
    }
  }, [shouldShowPoliticalExposureSelect]);

  useEffect(() => {
    // LOGGING ACTION
    if (productId) {
      logAction({
        productId,
        action: PURCHASE_FLOW_LOG_ACTIONS.choosingContactInfo,
      });
    }
  }, [productId]);

  return useMemo(
    () => ({
      contactPageLoaded:
        !pageUrlAndNavigationProcessing &&
        !pageAttributesLoading &&
        !applicationLoading &&
        Boolean(politicalExposures),
      processingContactPage,
      contactPageFormConfig,
      onContactFormSubmit,
      politicalExposureOptions: getPoliticalExposureOptions(),
      politicalExposuresLoading,
      visiblePageAttributes,
      userInfoValidationErrors,
      phonePrefix: phoneAreaCode ? `+${phoneAreaCode}` : regionPhonePrefix,
    }),
    [
      phoneAreaCode,
      userInfoValidationErrors,
      politicalExposuresLoading,
      visiblePageAttributes,
      getPoliticalExposureOptions,
      processingContactPage,
      contactPageFormConfig,
      onContactFormSubmit,
      pageUrlAndNavigationProcessing,
      pageAttributesLoading,
      applicationLoading,
      politicalExposures,
    ],
  );
};
