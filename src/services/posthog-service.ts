import type { MeUserFragment } from 'api/core/generated';
import { isDevelopment, POSTHOG_HOST, POSTHOG_KEY } from 'environment';
import posthog from 'posthog-js';

type RegisterPropertyInPostHogParams = {
  name: string;
  value: string;
};

const POSTHOG_IDENTIFIER_PROPERTY_NAME = 'identifier';

export const isPostHogEnabled = Boolean(POSTHOG_KEY && POSTHOG_HOST);

export const initializePostHog = () => {
  if (!isPostHogEnabled) {
    return;
  }

  posthog.init(POSTHOG_KEY, {
    api_host: POSTHOG_HOST,
    capture_exceptions: true,
    debug: isDevelopment,
    person_profiles: 'always',
    session_idle_timeout_seconds: 180, // 3 minutes
  });
};

export const registerPropertyInPostHog = ({
  name,
  value,
}: RegisterPropertyInPostHogParams) => {
  posthog.register({ [name]: value });
};

export const registerIdentifierPropertyInPostHog = (identifier: string) => {
  registerPropertyInPostHog({
    name: POSTHOG_IDENTIFIER_PROPERTY_NAME,
    value: identifier,
  });
};

type IdentifyUserInPosthogParams = {
  user: Nullable<MeUserFragment>;
  hashIdentifier: Nullable<string>;
};

export const identifyUserInPostHog = ({
  user,
  hashIdentifier,
}: IdentifyUserInPosthogParams) => {
  if (!user && !hashIdentifier) {
    return;
  }

  if (!user && hashIdentifier) {
    registerIdentifierPropertyInPostHog(hashIdentifier);
    return;
  }

  posthog.identify(user?.id.toString(), {
    userId: user?.id || 'No User ID',
    email: user?.email || 'No User Email',
    name: !(!user?.profile?.first_name && !user?.profile?.last_name)
      ? `${user.profile?.first_name ?? ''} ${
          user.profile?.last_name ?? ''
        }`.trimStart()
      : 'No User Name',
  });
};
