import { GOOGLE_ANALYTICS_ID, isProd } from '@config/envs';
import { userApi } from '@entities/user';
import type { MeUserFragment } from '@entities/user/api/queries.gen';
import { useCallback, useEffect, useState } from 'react';
import ReactGA from 'react-ga4';
import { useMount } from 'react-use';

export const isGoogleAnalyticsEnabled = Boolean(GOOGLE_ANALYTICS_ID);

export const useGoogleAnalytics = () => {
  const [isInitialized, setIsInitialized] = useState(false);

  const { data: user } = userApi.useSuspenseUserQuery(undefined, {
    select: (data) => data?.me ?? null,
  });

  const initialize = useCallback(() => {
    if (!GOOGLE_ANALYTICS_ID || isInitialized) return;

    ReactGA.initialize(GOOGLE_ANALYTICS_ID, {
      testMode: !isProd,
      gtagOptions: {
        debug_mode: !isProd,
      },
    });

    setIsInitialized(true);
  }, [isInitialized]);

  const identifyUser = useCallback(
    (user: Nullable<MeUserFragment>) => {
      if (!isInitialized || !user) return;

      ReactGA.set({
        user_id: user.id.toString(),
        custom_map: {
          dimension1: user.email ?? 'No User Email',
          dimension2: !(!user.profile?.first_name && !user.profile?.last_name)
            ? `${user.profile?.first_name ?? ''} ${user.profile?.last_name ?? ''}`.trimStart()
            : 'No User Name',
        },
      });
    },
    [isInitialized],
  );

  useMount(() => {
    initialize();
  });

  useEffect(() => {
    if (!user) return;
    identifyUser(user);
  }, [user]);

  return {
    isInitialized,
    identifyUser,
  };
};
