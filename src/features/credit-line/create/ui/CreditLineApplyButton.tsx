import { Button, type ButtonProps } from '@components/ui/button';
import { APP_CONFIG } from '@config/app';
import { PURCHASE_FLOW_ROUTE_NAME } from '@config/routes';
import { MarketingSearchParamValue } from '@config/search-params';
import { useGetAnalyticsCampaignParametersFromSearch } from '@entities/analytics';
import { addMarketingParamsToUrl } from '@utils/url';
import { type FC, useState } from 'react';

type CreditLineApplyButtonProps = {
  buttonElement?: FC<ButtonProps>;
} & Omit<ButtonProps, 'onClick'>;

export const CreditLineApplyButton: FC<CreditLineApplyButtonProps> = ({
  buttonElement: ButtonElement = Button,
  children,
  ...props
}) => {
  const [isProcessingApplication, setIsProcessingApplication] = useState(false);
  const currentUtmParams = useGetAnalyticsCampaignParametersFromSearch();

  const onClick = () => {
    setIsProcessingApplication(true);

    const url = APP_CONFIG.creditLine.isCreditAccountInterestFreeBannerEnabled
      ? PURCHASE_FLOW_ROUTE_NAME.creditLineInterestFree
      : PURCHASE_FLOW_ROUTE_NAME.creditLine;

    window.open(
      addMarketingParamsToUrl({
        url,
        currentUtmParams,
        fallbackUtmSource:
          MarketingSearchParamValue.UTM_SOURCE_CUSTOMER_PROFILE,
      }),
      '_self',
    );
    setTimeout(() => {
      setIsProcessingApplication(false);
    }, 1000);
  };

  return (
    <ButtonElement
      {...props}
      loading={isProcessingApplication}
      onClick={onClick}
    >
      {children}
    </ButtonElement>
  );
};
