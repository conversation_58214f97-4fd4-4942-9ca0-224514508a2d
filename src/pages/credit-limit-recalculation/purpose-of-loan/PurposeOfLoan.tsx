import {
  PurposeOfLoanType,
  type UpdateUserInfoMutationVariables,
} from 'api/core/generated';
import {
  IconNames,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_LOAN_TYPE_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { AppButton, AppCard, AppIcon, AppLoader } from 'components';
import { useRootContext } from 'context/root';
import {
  useEffectOnce,
  useGetCreditLimitRecalculation,
  useUpdateUserInfo,
} from 'hooks';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { formatCreditLimitRecalculationToPurposeOfLoanPageDataFormat } from 'services';

import styles from './PurposeOfLoan.module.scss';

const Page = () => {
  const { t } = useTranslation(LocizeNamespaces.common);
  const { t: tr } = useTranslation(LocizeNamespaces.purposeOfLoan);

  const { getPageUrlAndNavigate, pageUrlAndNavigationProcessing } =
    useRootContext();

  const {
    getCreditLimitRecalculation,
    creditLimitRecalculation,
    creditLimitRecalculationLoading,
  } = useGetCreditLimitRecalculation();

  const { updateUserInfo, userInfoUpdating } = useUpdateUserInfo();

  const [purposeOfLoan, setPurposeOfLoan] =
    useState<Nullable<PurposeOfLoanType>>(null);

  const isContinueDisabled =
    !(
      [
        PurposeOfLoanType.MARRIED_HOUSEHOLD,
        PurposeOfLoanType.MARRIED_PERSONAL,
      ] as Array<Nullable<PurposeOfLoanType>>
    ).includes(purposeOfLoan) || userInfoUpdating;

  const {
    applicationUserInfoId,
    email,
    phone,
    address,
    postCode,
    city,
    iban,
    purposeOfLoan: defaultPurposeOfLoan,
  } = formatCreditLimitRecalculationToPurposeOfLoanPageDataFormat(
    creditLimitRecalculation,
  );

  const onContinueButtonClick = async () => {
    const variables: UpdateUserInfoMutationVariables = {
      application_user_info_id: applicationUserInfoId ?? 0,
      purpose_of_loan: purposeOfLoan,
      email,
      phone,
      address,
      post_code: postCode,
      city,
      iban,
    };

    await updateUserInfo(variables);

    getPageUrlAndNavigate(true);
  };

  useEffectOnce(() => {
    getCreditLimitRecalculation();
  });

  useEffect(() => {
    setPurposeOfLoan(defaultPurposeOfLoan);
  }, [defaultPurposeOfLoan]);

  if (pageUrlAndNavigationProcessing || creditLimitRecalculationLoading) {
    return <AppLoader isRelative />;
  }
  return (
    <div className={styles.container}>
      <p className={styles.title}>
        {tr(LOCIZE_LOAN_TYPE_TRANSLATION_KEYS.pageTitle)}
      </p>

      <div className={styles.grid}>
        <AppCard
          active={purposeOfLoan === PurposeOfLoanType.MARRIED_HOUSEHOLD}
          onClick={() => setPurposeOfLoan(PurposeOfLoanType.MARRIED_HOUSEHOLD)}
        >
          <AppIcon className={styles.icon} name={IconNames.family} />
          <p className={styles.card__title}>
            {tr(LOCIZE_LOAN_TYPE_TRANSLATION_KEYS.familyLoan)}
          </p>
          <p className={styles.card__description}>
            {tr(LOCIZE_LOAN_TYPE_TRANSLATION_KEYS.familyLoanDescription)}
          </p>
        </AppCard>
        <AppCard
          active={purposeOfLoan === PurposeOfLoanType.MARRIED_PERSONAL}
          onClick={() => setPurposeOfLoan(PurposeOfLoanType.MARRIED_PERSONAL)}
        >
          <AppIcon className={styles.icon} name={IconNames.person} />
          <p className={styles.card__title}>
            {tr(LOCIZE_LOAN_TYPE_TRANSLATION_KEYS.personalLoan)}
          </p>
          <p className={styles.card__description}>
            {tr(LOCIZE_LOAN_TYPE_TRANSLATION_KEYS.personalLoanDescription)}
          </p>
        </AppCard>
      </div>

      <AppButton
        className={styles.button}
        isDisabled={isContinueDisabled}
        label={t(LOCIZE_COMMON_TRANSLATION_KEYS.continue)}
        onClick={onContinueButtonClick}
      />
    </div>
  );
};

export default Page;
